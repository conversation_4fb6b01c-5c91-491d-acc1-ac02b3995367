package com.lvpuhui.gic.common.service.impl;

import com.cloopen.rest.sdk.BodyType;
import com.cloopen.rest.sdk.CCPRestSmsSDK;
import com.lvpuhui.gic.common.service.SmsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class SmsServiceImpl implements SmsService {

    String serverIp = "app.cloopen.com";
    /**
     * 请求端口
     */
    String serverPort = "8883";
    /**
     * 主账号,登陆云通讯网站后,可在控制台首页看到开发者主账号ACCOUNT SID和主账号令牌AUTH TOKEN
     */
    String accountSId = "8aaf07087051bcec0170669ca5850cc6";
    String accountToken = "214224e50fde42bb88f72b655465642a";
    /**
     * 请使用管理控制台中已创建应用的APPID
     */
    String appId = "8aaf07087051bcec0170669ca5ec0ccd";
    String templateId= "1083921";

    String SUCCESS_CODE = "000000";

    @Override
    public void sendTemplateSMS(String mobile, String content) {
        CCPRestSmsSDK sdk = new CCPRestSmsSDK();
        sdk.init(serverIp, serverPort);
        sdk.setAccount(accountSId, accountToken);
        sdk.setAppId(appId);
        sdk.setBodyType(BodyType.Type_JSON);
        String[] datas = {content};
        HashMap<String, Object> result = sdk.sendTemplateSMS(mobile,templateId,datas);
        if(result.get("statusCode").equals(SUCCESS_CODE)){
            //正常返回输出data包体信息（map）
            HashMap<String,Object> data = (HashMap<String, Object>) result.get("data");
            Set<String> keySet = data.keySet();
            for(String key:keySet){
                Object object = data.get(key);
                log.info("{}={}",key ,object);
            }
        }else{
            log.error("{}","错误码=" + result.get("statusCode") +" 错误信息= "+result.get("statusMsg"));
            throw new RuntimeException("错误码=" + result.get("statusCode") +" 错误信息= "+result.get("statusMsg"));
        }
    }

    public static void main(String[] args) {
        SmsService smsService = new SmsServiceImpl();
        smsService.sendTemplateSMS("18612351795","111111");


    }

}
