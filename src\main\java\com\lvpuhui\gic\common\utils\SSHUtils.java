package com.lvpuhui.gic.common.utils;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.logging.Logger;

import com.jcraft.jsch.ChannelExec;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.Session;

public class SSHUtils {
	private static Logger logger = Logger.getLogger("SSHUtils");
	private static int timeout = 30000;
	
	public static String execute(String host, int port, String username, String password, String cmd) {
		Session session = null;
		StringBuffer msgCache = new StringBuffer();
		
		try {
			JSch jsch = new JSch();
			if(port <= 0) {
				session = jsch.getSession(username, host);
			} else {
				session = jsch.getSession(username, host, port);
			}
			session.setPassword(password);
			session.setConfig("StrictHostKeyChecking", "no");
			session.setTimeout(timeout); // 设置timeout时间
			session.connect(); // 通过Session建立与远程服务器的连接会话
			
			ChannelExec channelExec = (ChannelExec) session.openChannel("exec");
			channelExec.setCommand(cmd); //添加传入进来的shell命令
			channelExec.setInputStream(null);
			channelExec.setErrStream(System.err);//通道连接错误信息提示
			channelExec.connect();
			BufferedReader in = new BufferedReader(new InputStreamReader(channelExec.getInputStream()));
			String msg = null;
			while ((msg = in.readLine()) != null) {
				msgCache.append(msg);
			}
			in.close();
			channelExec.disconnect();
			session.disconnect();
			logger.info("command:" + cmd + " executed on " + host + " successfully!");
		} catch(Exception e) {
			logger.info(e.getMessage());
		}
		return msgCache.toString();
	}
}