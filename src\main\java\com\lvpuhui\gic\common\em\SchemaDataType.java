package com.lvpuhui.gic.common.em;

/**
 * 模型字段数据类型
 * <AUTHOR>
 *
 */
public enum SchemaDataType {

	NUMBER(0, "DOUBLE"), STRING(1, "STRING"), DATE(2, "TIMESTAMP");
	
	private int code;
	private String hiveDateType;
	
	private SchemaDataType(int code, String hiveDateType) {
		this.code = code;
		this.hiveDateType = hiveDateType;
	}
	
	public int getCode() {
		return code;
	}
	
	public String getHiveDateType() {
		return hiveDateType;
	}
	
	public static SchemaDataType getByCode(int code) {
		for(SchemaDataType type : SchemaDataType.values()) {
			if(type.getCode() == code) {
				return type;
			}
		}
		return null;
	}
}
