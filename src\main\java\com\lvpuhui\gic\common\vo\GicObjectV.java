package com.lvpuhui.gic.common.vo;

import java.io.Serializable;
import java.sql.Timestamp;

import com.alibaba.fastjson.JSONObject;
import com.lvpuhui.gic.common.po.GicObject;
import com.lvpuhui.gic.common.utils.DateUtils;


public class GicObjectV implements Serializable {
	private static final long serialVersionUID = 1L;
	private Long id;// id
	private Timestamp created;// 创建时间
	private String createdText;//创建时间文本格式
	private Long creator;// 创建者ID
	private String creatorName;//创建人名称
	private Timestamp updated;// 更新时间
	private String updatedText;//更新时间文本格式
	private Long updator;// 最后操作者ID
	private String updatorName;//最后操作者名称
	private Integer deleted;// 删除 0未删除 1已删除
	public Long getId() {
		return id;
	}
	
	public GicObjectV() {
		
	}
	
	public GicObjectV(GicObject object) {
		this.id = object.getId();
		this.created = object.getCreated();
		this.creator = object.getCreator();
		this.updated = object.getUpdated();
		this.updator = object.getUpdator();
		this.deleted = object.getDeleted();
		this.createdText = DateUtils.formatTime(this.created);
		this.updatedText = DateUtils.formatTime(this.updated);
		// TODO account name
		this.creatorName = "";
		this.updatorName = "";
	}
	
	public GicObjectV(JSONObject object) {
		this.id = object.getLong("id");
		this.created = object.getTimestamp("created");
		this.creator = object.getLong("creator");
		this.updated = object.getTimestamp("updated");
		this.updator = object.getLong("updator");
		this.deleted = object.getInteger("deleted");
		this.createdText = object.getString("createdText");
		this.updatedText = object.getString("updatedText");
		// TODO account name
		this.creatorName =  object.getString("creatorName");
		this.updatorName = object.getString("updatorName");
	}
	
	public void setId(Long id) {
		this.id = id;
	}
	
	public Timestamp getCreated() {
		return created;
	}
	
	public void setCreated(Timestamp created) {
		this.created = created;
	}
	
	public String getCreatedText() {
		return createdText;
	}
	
	public void setCreatedText(String createdText) {
		this.createdText = createdText;
	}
	
	public Long getCreator() {
		return creator;
	}
	
	public void setCreator(Long creator) {
		this.creator = creator;
	}
	
	public String getCreatorName() {
		return creatorName;
	}
	
	public void setCreatorName(String creatorName) {
		this.creatorName = creatorName;
	}
	
	public Timestamp getUpdated() {
		return updated;
	}
	
	public void setUpdated(Timestamp updated) {
		this.updated = updated;
	}
	
	public String getUpdatedText() {
		return updatedText;
	}
	
	public void setUpdatedText(String updatedText) {
		this.updatedText = updatedText;
	}
	
	public Long getUpdator() {
		return updator;
	}
	
	public void setUpdator(Long updator) {
		this.updator = updator;
	}
	
	public String getUpdatorName() {
		return updatorName;
	}
	
	public void setUpdatorName(String updatorName) {
		this.updatorName = updatorName;
	}
	
	public Integer getDeleted() {
		return deleted;
	}
	
	public void setDeleted(Integer deleted) {
		this.deleted = deleted;
	}
}
