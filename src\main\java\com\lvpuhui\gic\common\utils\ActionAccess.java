package com.lvpuhui.gic.common.utils;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import com.lvpuhui.gic.common.em.Operate;
import com.lvpuhui.gic.common.em.Resource;
import com.lvpuhui.gic.common.em.UserType;

@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface ActionAccess {
	/**
	 * 微服务名
	 * @return
	 */
    Resource resource();
    
    /**
     * 操作类型
     * @return
     */
    Operate operate();
    
    /**
     * 用户类型
     * @return
     */
    UserType[] userType();
}