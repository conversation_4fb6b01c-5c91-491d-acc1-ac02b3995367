package com.lvpuhui.gic.common.vo;

import com.lvpuhui.gic.common.po.NameOnly;

public class NameOnlyV implements NameOnly {

	private Long id;
	private String name;

	public NameOnlyV() {
		super();
	}

	public NameOnlyV(Long id, String name) {
		super();
		this.id = id;
		this.name = name;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public void setName(String name) {
		this.name = name;
	}

	@Override
	public Long getId() {
		return id;
	}

	@Override
	public String getName() {
		return name;
	}

}
