package com.lvpuhui.gic.common.operationlog;

import com.lvpuhui.gic.common.utils.MessageUtils;

public enum OperationType {
	CREATE("operation_type_CREATE"), DELETE("operation_type_DELETE"), UPDATE("operation_type_UPDATE"),
	LOGIN("operation_type_LOGIN");

	private String name;

	private OperationType(String name) {
		this.name = name;
	}

	public String getName() {
		return name;
	}

	public String getI18nName() {
		return MessageUtils.get(name);
	}
}
