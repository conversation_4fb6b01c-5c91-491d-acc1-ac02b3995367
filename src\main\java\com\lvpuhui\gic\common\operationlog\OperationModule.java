package com.lvpuhui.gic.common.operationlog;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import com.lvpuhui.gic.common.utils.MessageUtils;

public enum OperationModule {
	SECURITY("operation_module_SECURITY", 0, 1), ACCESS("operation_module_ACCESS", 0),
	ORGANIZATION("operation_module_ORGANIZATION", 0), ROLE("operation_module_ROLE", 0), STAFF("operation_module_STAFF", 0),
	ANNOUNCEMENT("operation_module_ANNOUNCEMENT", 0), CUSTOMER("operation_module_CUSTOMER", 0),
	MODEL("operation_module_MODEL", 0), SCENARIO("operation_module_SCENARIO", 0), APPLICATION("operation_module_APPLICATION", 1),
	ACT("operation_module_ACT", 1), ATTACH("operation_module_ATTACH", 0, 1), TENANT("operation_module_TENANT", 0),
	TENANT_ACCOUNT("operation_module_TENANT_ACCOUNT", 1), ENTERPRISE_INFORM("operation_module_ENTERPRISE_INFORM", 1);

	private String name;
	private int[] types;//日志适用的用户类型 0=staff 1=tenant

	private OperationModule(String name, int... types) {
		this.name = name;
		this.types = types;
	}

	public String getName() {
		return name;
	}

	public String getI18nName() {
		return MessageUtils.get(name);
	}
	
	public static List<OperationModule> getValuesByType(int type) {
		List<OperationModule> modules = new ArrayList<>();
		for (OperationModule om : OperationModule.values()) {
			if(om.types == null) {
				continue;
			}
			if(Arrays.binarySearch(om.types, type) >= 0) {
				modules.add(om);
			}
		}
		return modules;
	}
}
