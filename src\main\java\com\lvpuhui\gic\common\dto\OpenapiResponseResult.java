package com.lvpuhui.gic.common.dto;

public class OpenapiResponseResult<T> {

	private final static int STATE_OK = 0;
	private final static int STATE_FAIL = 1;

	private int code;// [0:成功|1:失败]
	private String message;
	private T data;

	OpenapiResponseResult() {
	}

	private OpenapiResponseResult(int code) {
		this.code = code;
	}

	private OpenapiResponseResult(int code, String message, T data) {
		this.code = code;
		this.message = message;
		this.data = data;
	}

	public static <T> OpenapiResponseResult<T> ok() {
		return new OpenapiResponseResult<>(STATE_OK);
	}

	public static <T> OpenapiResponseResult<T> ok(T body) {
		return new OpenapiResponseResult<>(STATE_OK, "success", body);
	}

	public static <T> OpenapiResponseResult<T> ok(T body, String msg) {
		return new OpenapiResponseResult<>(STATE_OK, msg, body);
	}

	public static <T> OpenapiResponseResult<T> failed() {
		return new OpenapiResponseResult<>(STATE_FAIL);
	}

	public static <T> OpenapiResponseResult<T> failed(String msg, T body) {
		return new OpenapiResponseResult<>(STATE_FAIL, msg, body);
	}

	public static <T> OpenapiResponseResult<T> failed(String msg) {
		return new OpenapiResponseResult<>(STATE_FAIL, msg, null);
	}

	public int getCode() {
		return code;
	}

	public void setCode(int code) {
		this.code = code;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public T getData() {
		return data;
	}

	public void setData(T data) {
		this.data = data;
	}
}
