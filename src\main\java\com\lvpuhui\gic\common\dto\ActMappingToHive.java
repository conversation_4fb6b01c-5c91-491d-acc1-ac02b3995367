package com.lvpuhui.gic.common.dto;

/**
 * 行为映射到hive的存储对象
 * 
 * <AUTHOR>
 *
 */
public class ActMappingToHive {
	private String schemaName;// 模型字段名
	private String actField;// 行为字段名
	private Integer type;// 字段类型:0数值，1字符,2日期
	private Integer length;// 长度
	private Integer pointLength;// 小数点长度
	private String defaultValue;// 默认值
	private Boolean isAuxiliary;//辅助字段

	public String getSchemaName() {
		return schemaName;
	}

	public void setSchemaName(String schemaName) {
		this.schemaName = schemaName;
	}

	public String getActField() {
		return actField;
	}

	public void setActField(String actField) {
		this.actField = actField;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public Integer getLength() {
		return length;
	}

	public void setLength(Integer length) {
		this.length = length;
	}

	public Integer getPointLength() {
		return pointLength;
	}

	public void setPointLength(Integer pointLength) {
		this.pointLength = pointLength;
	}

	public String getDefaultValue() {
		return defaultValue;
	}

	public void setDefaultValue(String defaultValue) {
		this.defaultValue = defaultValue;
	}

	public Boolean getIsAuxiliary() {
		return isAuxiliary;
	}

	public void setIsAuxiliary(Boolean isAuxiliary) {
		this.isAuxiliary = isAuxiliary;
	}
}
