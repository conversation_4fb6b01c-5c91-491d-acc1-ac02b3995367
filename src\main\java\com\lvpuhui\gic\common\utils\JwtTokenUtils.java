package com.lvpuhui.gic.common.utils;

import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import org.jasypt.util.text.BasicTextEncryptor;

import com.lvpuhui.gic.common.po.User;

public class JwtTokenUtils {
	
	public static String secretKey = "@2021~intcube&&lvpuhui#2021&";
	public static String tokenSecreKey = "lvpuhui@2021";
	
//	public static String getToken(User user) {
//		//expires time 30minute
//		Calendar nowTime = Calendar.getInstance();
//		nowTime.add(Calendar.MINUTE, 30);
//		Date expiresDate = nowTime.getTime();
//
//		Map<String,Object> session = new HashMap<String,Object>();
//		session.put("userId", user.getId());
//		session.put("loginName", user.getLoginName());
//		session.put("type", user.getType());
//        String token= JWT.create()
//        		.withAudience(String.valueOf(user.getId()))//接收者
//        		.withExpiresAt(expiresDate)//过期时间
//        		.withIssuer("lvpuhui")//颁发者
//        		.withIssuedAt(new Date())//颁发时间
//        		.withClaim("session", session)//负载
//                .sign(Algorithm.HMAC256(secretKey));//签名
//        BasicTextEncryptor encryptor = new BasicTextEncryptor();
//        encryptor.setPassword(tokenSecreKey);
//        token = encryptor.encrypt(token);
//        return token;
//    }
}
