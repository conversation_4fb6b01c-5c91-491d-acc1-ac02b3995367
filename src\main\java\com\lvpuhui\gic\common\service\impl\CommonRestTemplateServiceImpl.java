package com.lvpuhui.gic.common.service.impl;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import com.alibaba.fastjson.JSONObject;
import com.lvpuhui.gic.common.em.ServiceUrl;
import com.lvpuhui.gic.common.operationlog.OperationLogDto;
import com.lvpuhui.gic.common.service.CommonRestTemplateService;
import com.lvpuhui.gic.common.utils.CommonUtils;

@Service
public class CommonRestTemplateServiceImpl implements CommonRestTemplateService {
	
	private final static Logger logger = LoggerFactory.getLogger(CommonRestTemplateServiceImpl.class);

	@Autowired
	private RestTemplate restTemplate;
	
	private <T, B> T request(String url, HttpMethod method, B body, Class<T> t) {
		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.APPLICATION_JSON);
		headers.add("token", CommonUtils.getToken());
		HttpEntity<B> entity = new HttpEntity<B>(body, headers);
		ResponseEntity<T> resp = restTemplate.exchange(url, method, entity, t);
		return resp.getBody();
	}

	@Override
	public void saveOperationLog(OperationLogDto operationLog) {
		JSONObject result = request(ServiceUrl.OPERATIONLOG.getUrl() + "/operationlog/info", HttpMethod.POST, operationLog, JSONObject.class);
		logger.info("saveOperationLog.result:" + result.toString());
	}
}
