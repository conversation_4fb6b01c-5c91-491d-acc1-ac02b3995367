package com.lvpuhui.gic.common.em;

/**
 * 服务名
 * 
 * <AUTHOR>
 *
 */
public enum Service {
	ADMINISTRATIVE("carbonbook.administrative"), ENTERPRISE("carbonbook.enterprise"), TENANT("carbonbook.tenant"),
	ACCOUNT("carbonbook.account"), FILE("carbonbook.file"), ANALYSIS("carbonbook.analysis"), OPERATIONLOG("carbonbook.operationlog");

	private String name;

	private Service(String name) {
		this.name = name;
	}

	public String getName() {
		return name;
	}
}
