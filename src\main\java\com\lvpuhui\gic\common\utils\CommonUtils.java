package com.lvpuhui.gic.common.utils;

import java.io.UnsupportedEncodingException;
import java.lang.reflect.Array;
import java.util.Collection;
import java.util.Map;
import java.util.UUID;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.lvpuhui.gic.common.po.User;

/**
 * 公共工具
 * <AUTHOR>
 *
 */
public class CommonUtils {
	
	/**
	 * md5
	 * @param value
	 * @return
	 * @throws UnsupportedEncodingException 
	 */
	public static String md5(String value) throws UnsupportedEncodingException {
		return DigestUtils.md5Hex(value.getBytes("UTF-8")).toUpperCase();
	}

	/**
	 * 创建UUID
	 * @return
	 */
	public static String createUUID() {
		UUID uuid = UUID.randomUUID();
		return uuid.toString();
	}
	
	/**
	 * 判断对象, 集合, Map, 数组, 字符串, StringBuffer是否为空
	 * @param obj
	 * @return
	 */
	public static boolean isNotNullOrEmpty(Object obj) {
		if(null == obj) {
			return false;
		}
		if(obj instanceof Collection) {
			if (((Collection)obj).size() == 0) {
				return false;
			}
		} else if (obj instanceof String) {
			if (((String)obj).trim().equals("")) {
				return false;
			}
		} else if (obj instanceof Map) {
			if (((Map)obj).size() == 0) {
				return false;
			}
		} else if (obj.getClass().isArray()) {
			if(Array.getLength(obj) == 0) {
				return false;
			}
		} else if (obj instanceof StringBuffer) {
			if (((StringBuffer)obj).length() == 0) {
				return false;
			}
		}
		return true;
	}
	
	public static String getIpAddr(HttpServletRequest request) {
		String ip = getReqStrParam(request, "ipAddress");
		if(isNotNullOrEmpty(ip)) {
			return ip;
		}
		ip = request.getHeader("x-forwarded-for");
		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getHeader("PRoxy-Client-IP");
		}
		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getHeader("WL-Proxy-Client-IP");
		}
		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getRemoteAddr();
		}
		if(isNotNullOrEmpty(ip) && ip.equals("0:0:0:0:0:0:0:1")){
			ip = request.getServerName();
		}
		return ip;
	}
	
    private static String getReqStrParam(HttpServletRequest req, String key) {
    	String value = req.getParameter(key);
        String v = value == null ? "" : value.trim();
        if ("null".equals(v) || "undefined".equals(v)){
            v = "";
        }
        return v;
    }
    
    public static <T> T getReqAttribute(String key, Class<T> t) {
    	ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes)RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = servletRequestAttributes.getRequest();
        return (T)request.getAttribute(key);
    }
    
    public static Long getCurrentUserId() {
    	String userId = getReqAttribute("userId", String.class);
    	if(CommonUtils.isNotNullOrEmpty(userId)) {
    		return Long.parseLong(userId);
    	}
    	return 0l;
    }
    
    public static User getCurrentUser() {
    	Map<String, Object> session = getReqAttribute("session", Map.class);
    	if(isNotNullOrEmpty(session)) {
    		User user = new User();
    		user.setId(Long.parseLong(session.get("userId").toString()));
    		user.setLoginName((String)session.get("loginName"));
    		user.setType(Integer.parseInt(session.get("type").toString()));
    		return user;
    	}
    	return null;
    }
    
    /**
     * 服务内部获取当前登录用户token，供服务间调用使用
     * @return
     */
    public static String getToken() {
    	return getReqAttribute("token", String.class);
    }
}
