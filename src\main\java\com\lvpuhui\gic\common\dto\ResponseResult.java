package com.lvpuhui.gic.common.dto;

public class ResponseResult<T> {
	
	private final static int STATE_OK = 0;
	private final static int STATE_FAIL = 1;

	private int state;// [0:成功|1:失败]
	private String msg;
	private T data;

	ResponseResult() {
	}

	private ResponseResult(int state) {
		this.state = state;
	}

	private ResponseResult(int state, String msg, T data) {
		this.state = state;
		this.msg = msg;
		this.data = data;
	}

	public static <T> ResponseResult<T> ok() {
		return new ResponseResult<>(STATE_OK);
	}

	public static <T> ResponseResult<T> ok(T body) {
		return new ResponseResult<>(STATE_OK, null, body);
	}

	public static <T> ResponseResult<T> ok(T body, String msg) {
		return new ResponseResult<>(STATE_OK, msg, body);
	}

	public static <T> ResponseResult<T> failed() {
		return new ResponseResult<>(STATE_FAIL);
	}

	public static <T> ResponseResult<T> failed(String msg, T body) {
		return new ResponseResult<>(STATE_FAIL, msg, body);
	}

	public static <T> ResponseResult<T> failed(String msg) {
		return new ResponseResult<>(STATE_FAIL, msg, null);
	}

	public int getState() {
		return state;
	}

	public void setState(int state) {
		this.state = state;
	}

	public String getMsg() {
		return msg;
	}

	public void setMsg(String msg) {
		this.msg = msg;
	}

	public T getData() {
		return data;
	}

	public void setData(T data) {
		this.data = data;
	}
}
