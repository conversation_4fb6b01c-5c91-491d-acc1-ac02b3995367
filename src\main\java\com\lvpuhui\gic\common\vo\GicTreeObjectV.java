package com.lvpuhui.gic.common.vo;

import com.lvpuhui.gic.common.po.GicTreeObject;

public class GicTreeObjectV extends GicObjectV {

	private static final long serialVersionUID = 1L;

	private Long parentId;
	private String parentName;

	public GicTreeObjectV() {
		super();
	}

	public GicTreeObjectV(GicTreeObject object) {
		super(object);
		this.parentId = object.getParentId();
	}

	public Long getParentId() {
		return parentId;
	}

	public void setParentId(Long parentId) {
		this.parentId = parentId;
	}

	public String getParentName() {
		return parentName;
	}

	public void setParentName(String parentName) {
		this.parentName = parentName;
	}
}
