package com.lvpuhui.gic.common.exception;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import com.lvpuhui.gic.common.dto.ResponseResult;
import com.lvpuhui.gic.common.utils.MessageUtils;

/**
 * Controller异常处理
 * 
 * <AUTHOR>
 *
 */
@RestControllerAdvice
public class ExceptionHandlerController {
	
	private final static Logger logger = LoggerFactory.getLogger(ExceptionHandlerController.class);
	
	@ExceptionHandler(Exception.class) 
	public ResponseResult<String> handlerException(HttpServletRequest request, HttpServletResponse response, Exception e) {
		logger.error(null, e);
		String msg = e.getMessage();
		if(StringUtils.isEmpty(msg)) {
			msg = MessageUtils.get("common_unknow_error");
		}
		return ResponseResult.failed(msg);
	}

	@ExceptionHandler(MethodArgumentNotValidException.class)
	public ResponseResult<String> handlerValidException(HttpServletRequest request, HttpServletResponse response,
			MethodArgumentNotValidException e) {
		logger.error(null, e);
		BindingResult result = e.getBindingResult();
		String message = "";
		for (int i = 0; i < result.getFieldErrors().size(); i++) {
			FieldError fieldError = result.getFieldErrors().get(i);
			String prompt = fieldError.getDefaultMessage();
			if(prompt.contains(",")) {
				String[] arr = prompt.split(",");
				for (int k = 0; k < arr.length; k++) {
					message += MessageUtils.get(arr[k]);
				}
				message += ";";
			} else {
				message += MessageUtils.get(prompt) + ";";
			}
		}
		return ResponseResult.failed(message);
	}
}
