package com.lvpuhui.gic.common.exception;

import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import com.lvpuhui.gic.common.dto.ResponseResult;
import com.lvpuhui.gic.common.utils.MessageUtils;

/**
 * Controller异常处理
 * 
 * <AUTHOR>
 *
 */
@Data
@RestControllerAdvice
public class ExceptionHandlerController {
	
	private final static Logger logger = LoggerFactory.getLogger(ExceptionHandlerController.class);
	
	@ExceptionHandler(Exception.class) 
	public ResponseResult<String> handlerException(Exception e) {
		logger.error(null, e);
		String msg = e.getMessage();
		if(!StringUtils.hasText(msg)) {
			msg = MessageUtils.get("common_unknow_error");
		}
		return ResponseResult.failed(msg);
	}

	@ExceptionHandler(MethodArgumentNotValidException.class)
	public ResponseResult<String> handlerValidException(MethodArgumentNotValidException e) {
		logger.error(null, e);
		BindingResult result = e.getBindingResult();
		StringBuilder message = new StringBuilder();
		for (int i = 0; i < result.getFieldErrors().size(); i++) {
			FieldError fieldError = result.getFieldErrors().get(i);
			String prompt = fieldError.getDefaultMessage();
            if(StringUtils.hasText(prompt) && prompt.contains(",")){
                String[] arr = prompt.split(",");
                for (String s : arr) {
                    message.append(MessageUtils.get(s));
                }
                message.append(";");
                continue;
            }
            message.append(MessageUtils.get(prompt)).append(";");
		}
		return ResponseResult.failed(message.toString());
	}
}
