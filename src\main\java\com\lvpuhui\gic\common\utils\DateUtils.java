package com.lvpuhui.gic.common.utils;

import java.text.ParseException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * 时间日期处理
 * <AUTHOR>
 *
 */
public class DateUtils {

	private final static String PATTERN_TIME = "yyyy-MM-dd HH:mm:ss";
	private final static String PATTERN_DATE = "yyyy-MM-dd";

	
	/**
	 * time字符串
	 * @param date
	 * @return
	 */
	public static String formatTime(Date date) {
		if(date == null) {
			return null;
		}
		Instant instant = date.toInstant();
		LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
		
		return localDateTime.format(DateTimeFormatter.ofPattern(PATTERN_TIME));
	}
	
	public static Date parseTime(String source) throws ParseException {
		LocalDateTime localDateTime = LocalDateTime.parse(source, DateTimeFormatter.ofPattern(PATTERN_TIME));
        Instant instant = localDateTime.atZone(ZoneId.systemDefault()).toInstant();
        return Date.from(instant);
	}
	
	/**
	 * 日期字符串
	 * @param date
	 * @return
	 */
	public static String formatDate(Date date) {
		if(date == null) {
			return null;
		}
		Instant instant = date.toInstant();
		LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
		
		return localDateTime.format(DateTimeFormatter.ofPattern(PATTERN_DATE));
	}
}
