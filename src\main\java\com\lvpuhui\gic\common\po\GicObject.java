package com.lvpuhui.gic.common.po;

import java.io.Serializable;
import java.sql.Timestamp;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@MappedSuperclass
@JsonIgnoreProperties(value= {"hibernateLazyInitializer", "handler"})
public class GicObject implements Serializable {

	private static final long serialVersionUID = 1L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "ID")
	private Long id;// id

	@Column(name = "CREATED", updatable = false)
	private Timestamp created;// 创建时间

	@Column(name = "CREATOR", updatable = false)
	private Long creator;// 创建者ID

	@Column(name = "UPDATED")
	private Timestamp updated;// 更新时间

	@Column(name = "UPDATOR")
	private Long updator;// 最后操作者ID

	@Column(name = "DELETED")
	private Integer deleted;// 删除 0未删除 1已删除

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Timestamp getCreated() {
		return created;
	}

	public void setCreated(Timestamp created) {
		this.created = created;
	}

	public Long getCreator() {
		return creator;
	}

	public void setCreator(Long creator) {
		this.creator = creator;
	}

	public Timestamp getUpdated() {
		return updated;
	}

	public void setUpdated(Timestamp updated) {
		this.updated = updated;
	}

	public Long getUpdator() {
		return updator;
	}

	public void setUpdator(Long updator) {
		this.updator = updator;
	}

	public Integer getDeleted() {
		return deleted;
	}

	public void setDeleted(Integer deleted) {
		this.deleted = deleted;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		GicObject other = (GicObject) obj;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		return true;
	}

	/**
	 * 新增操作
	 * @param accountId
	 * @param time
	 */
	public void createOperate(User user, Timestamp time) {
		this.creator = user.getId();
		this.created = time;
		this.updator = user.getId();
		this.updated = time;
		this.deleted = 0;
	}
	
	/**
	 * 更新操作
	 * @param accountId
	 * @param time
	 */
	public void updateOperate(User user, Timestamp time) {
		this.updator = user.getId();
		this.updated = time;
	}
}
