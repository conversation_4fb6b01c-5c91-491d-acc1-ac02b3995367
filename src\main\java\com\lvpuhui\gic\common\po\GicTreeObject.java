package com.lvpuhui.gic.common.po;

import jakarta.persistence.Column;
import jakarta.persistence.MappedSuperclass;

/**
 * 层级对象
 * <AUTHOR>
 *
 */
@MappedSuperclass
public class GicTreeObject extends GicObject {

	private static final long serialVersionUID = 1L;
	
	@Column(name = "PARENT_ID")
	private Long parentId;

	public Long getParentId() {
		return parentId;
	}

	public void setParentId(Long parentId) {
		this.parentId = parentId;
	}
}
