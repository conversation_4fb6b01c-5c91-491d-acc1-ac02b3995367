package com.lvpuhui.gic.common.utils;

import cn.hutool.jwt.JWT;
import cn.hutool.jwt.JWTUtil;
import cn.hutool.jwt.signers.JWTSigner;
import cn.hutool.jwt.signers.JWTSignerUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.lvpuhui.gic.common.em.DefaultStaff;
import com.lvpuhui.gic.common.em.Resource;
import com.lvpuhui.gic.common.em.UserType;
import com.lvpuhui.gic.common.exception.GicException;
import org.jasypt.util.text.BasicTextEncryptor;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.util.pattern.PathPatternParser;

import java.lang.reflect.Method;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;


public class AuthenticationInterceptor implements HandlerInterceptor {

    private static AntPathMatcher antPathMatcher = new AntPathMatcher();

    private static String secretKey = "@2021~intcube&&lvpuhui#2021&";
    private static String tokenSecretKey = "lvpuhui@2021";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object object) throws Exception {
    	String token = request.getHeader("token");
        if(skip(request)) {
            return true;
        }
        if(!(object instanceof HandlerMethod handlerMethod)){
            return true;
        }
        Method method=handlerMethod.getMethod();
        if (method.isAnnotationPresent(PassToken.class)) {//skip token check
            PassToken passToken = method.getAnnotation(PassToken.class);
            if (passToken != null && passToken.required()) {
                return true;
            }
        }
        if (token == null) {
        	response.sendError(HttpServletResponse.SC_UNAUTHORIZED, "Unauthorized");
        	throw new RuntimeException("Unauthorized");
        }
        String originToken = token;
        BasicTextEncryptor encryptor = new BasicTextEncryptor();
        encryptor.setPassword(JwtTokenUtils.tokenSecreKey);
        token = encryptor.decrypt(token);

        JWTSigner signer = JWTSignerUtil.hs256(JwtTokenUtils.secretKey.getBytes(StandardCharsets.UTF_8));
        try {
            boolean verify = JWTUtil.verify(token, signer);
            if(!verify){
                response.sendError(HttpServletResponse.SC_UNAUTHORIZED, "Unauthorized");
                throw new RuntimeException("Unauthorized");
            }
            JWT parseToken = JWTUtil.parseToken(token);
            cn.hutool.json.JSONObject payloads = parseToken.getPayloads();
            String userId = payloads.getStr("aud");
            cn.hutool.json.JSONObject session = payloads.getJSONObject("session");
            // 校验权限
            if(method.isAnnotationPresent(ActionAccess.class)) {
                ActionAccess access = method.getAnnotation(ActionAccess.class);
                checkStaffAccess(session, access);
            }
            request.setAttribute("userId", userId);
            request.setAttribute("session", session);
            request.setAttribute("token", originToken);
        }catch (Exception e){
            response.sendError(HttpServletResponse.SC_UNAUTHORIZED, "Unauthorized");
            throw new RuntimeException("Unauthorized");
        }
        return true;
    }

    private void checkStaffAccess(Map<String,Object> session, ActionAccess staffAccess) {
    	Long userId = Long.parseLong(session.get("userId").toString());
    	Integer type = Integer.parseInt(session.get("type").toString());
    	UserType[] userTypes = staffAccess.userType();
    	List<Integer> userTypes2 = new ArrayList<>();
        for (UserType userType : userTypes) {
            userTypes2.add(userType.getValue());
        }
    	if(type == 1) {
    		// 租户账户只允许访问自身resource或staff资源的查询权限
    		if(userTypes2.contains(type)) {
    			return;
    		}
    	} else {//staff
    		if(userId.equals(DefaultStaff.ADMIN.getId())) {
    			return;
    		}
    		if(userTypes2.contains(type)) {
    			Resource resource = staffAccess.resource();
    			String access = (String) session.get("access");
    			if(CommonUtils.isNotNullOrEmpty(access)) {
    				JSONObject accessJson = JSON.parseObject(access);
    				
    				if(accessJson.containsKey(resource.getIdStr())) {
    					JSONArray as = accessJson.getJSONArray(resource.getIdStr());
    					// [0,0,0,1] ordinal()为下标 0=false 1=true
    					Integer a = as.getInteger(staffAccess.operate().ordinal());
    					if(a != null && a == 1) {
    						return;
    					}
    				}
    			}
    		}
    	}
    	// false
    	throw new GicException("No Access");
	}

    private final static String[] SKIP_PATHS = {
            "/v3/api-docs/*",
            "/doc.html",
            "/swagger-resources/**",
            "/swagger/**",
            "/**/v2/api-docs",
            "/**/*.js",
            "/**/*.css",
            "/**/*.png",
            "/**/*.ico",
            "/feignService/pullHiveData",
            "/webjars/springfox-swagger-ui/**",
            "/error"
    };

    private boolean skip(HttpServletRequest request) {
        String path = request.getServletPath();
        for (int i = 0; i < SKIP_PATHS.length; i++) {
            if(antPathMatcher.match(SKIP_PATHS[i],path)) {
                return true;
            }
        }
        return false;
    }
}
