package com.lvpuhui.gic.common.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.lvpuhui.gic.common.em.DefaultStaff;
import com.lvpuhui.gic.common.em.Resource;
import com.lvpuhui.gic.common.em.UserType;
import com.lvpuhui.gic.common.exception.GicException;
import org.jasypt.util.text.BasicTextEncryptor;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;


public class AuthenticationInterceptor extends HandlerInterceptorAdapter {

    private static AntPathMatcher antPathMatcher = new AntPathMatcher();
    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object object) throws Exception {
    	String token = request.getHeader("token");
        if(skip(request)) {
            return true;
        }
        if(!(object instanceof HandlerMethod)){
            return true;
        }
        HandlerMethod handlerMethod=(HandlerMethod)object;
        Method method=handlerMethod.getMethod();
        if (method.isAnnotationPresent(PassToken.class)) {//skip token check
            PassToken passToken = method.getAnnotation(PassToken.class);
            if (passToken.required()) {
                return true;
            }
        }
        if (token == null) {
        	response.sendError(HttpServletResponse.SC_UNAUTHORIZED, "Unauthorized");
        	throw new RuntimeException("Unauthorized");
        }
        String sToken = token;
        BasicTextEncryptor encryptor = new BasicTextEncryptor();
        encryptor.setPassword(JwtTokenUtils.tokenSecreKey);
        token = encryptor.decrypt(token);
        JWTVerifier jwtVerifier = JWT.require(Algorithm.HMAC256(JwtTokenUtils.secretKey)).build();
        try {
            jwtVerifier.verify(token);
            DecodedJWT decodedJWT = JWT.decode(token);
            String userId = decodedJWT.getAudience().get(0);
            Map<String,Object> session = decodedJWT.getClaim("session").asMap();
            // 校验权限
            if(method.isAnnotationPresent(ActionAccess.class)) {
            	ActionAccess access = method.getAnnotation(ActionAccess.class);
            	checkStaffAccess(session, access);
            }
            request.setAttribute("userId", userId);
            request.setAttribute("session", session);
            request.setAttribute("token", sToken);
        } catch (JWTVerificationException e) {
            response.sendError(HttpServletResponse.SC_UNAUTHORIZED, "Unauthorized");
            throw new RuntimeException("Unauthorized");
        }
        return true;
    }

    private boolean checkStaffAccess(Map<String,Object> session, ActionAccess staffAccess) {
    	Long userId = Long.parseLong(session.get("userId").toString());
    	Integer type = Integer.parseInt(session.get("type").toString());
    	UserType[] userTypes = staffAccess.userType();
    	List<Integer> userTypes2 = new ArrayList<>();
    	for (int i = 0; i < userTypes.length; i++) {
    		userTypes2.add(userTypes[i].getValue());
		}
    	if(type == 1) {
    		// 租户账户只允许访问自身resource或staff资源的查询权限
    		if(userTypes2.contains(type)) {
    			return true;
    		}
    	} else {//staff
    		if(userId.equals(DefaultStaff.ADMIN.getId())) {
    			return true;
    		}
    		if(userTypes2.contains(type)) {
    			Resource resource = staffAccess.resource();
    			String access = (String) session.get("access");
    			if(CommonUtils.isNotNullOrEmpty(access)) {
    				JSONObject accessJson = JSON.parseObject(access);
    				
    				if(accessJson.containsKey(resource.getIdStr())) {
    					JSONArray as = accessJson.getJSONArray(resource.getIdStr());
    					// [0,0,0,1] ordinal()为下标 0=false 1=true
    					Integer a = as.getInteger(staffAccess.operate().ordinal());
    					if(a != null && a == 1) {
    						return true;
    					}
    				}
    			}
    		}
    	}
    	// false
    	throw new GicException("No Access");
	}

    private final static String[] SKIP_PATHS = {
            "/v3/api-docs/*",
            "/doc.html",
            "/swagger-resources/**",
            "/swagger/**",
            "/**/v2/api-docs",
            "/**/*.js",
            "/**/*.css",
            "/**/*.png",
            "/**/*.ico",
            "/feignService/pullHiveData",
            "/webjars/springfox-swagger-ui/**",
            "/error"
    };

    private boolean skip(HttpServletRequest request) {
        String path = request.getServletPath();
        for (int i = 0; i < SKIP_PATHS.length; i++) {
            if(antPathMatcher.match(SKIP_PATHS[i],path)) {
                return true;
            }
        }
        return false;
    }
}
