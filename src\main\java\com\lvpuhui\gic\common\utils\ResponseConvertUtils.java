package com.lvpuhui.gic.common.utils;

import java.util.ArrayList;
import java.util.List;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.lvpuhui.gic.common.exception.GicException;
import com.lvpuhui.gic.common.vo.PageV;

/**
 * 服务间调用返回值处理
 * <AUTHOR>
 *
 */
public class ResponseConvertUtils {

	/**
	 * model
	 * @param response
	 * @param clazz
	 * @return
	 */
	protected <T> T convertToBean(JSONObject result, Class<T> clazz) {
		if(result.getIntValue("state") == 0) {
			return result.getObject("data", clazz);
		} else {
			throw new GicException(result.getString("msg"));
		}
	}
	
	/**
	 * List
	 * @param response
	 * @param clazz
	 * @return
	 */
	protected <T> List<T> convertToList(JSONObject result, Class<T> clazz) {
		if(result.getIntValue("state") == 0) {
			JSONArray data = result.getJSONArray("data");
			if(CommonUtils.isNotNullOrEmpty(data)) {
				return data.toJavaList(clazz);
			}
			return new ArrayList<T>();
		} else {
			throw new GicException(result.getString("msg"));
		}
	}
	
	/**
	 * 分页
	 * @param response
	 * @param clazz
	 * @return
	 */
	protected <T> PageV<T> convertToPage(JSONObject result, Class<T> clazz) {
		JSONObject pageV = convertToJSON(result);
		PageV<T> resultV = new PageV<>();
		resultV.setPage(pageV.getIntValue("page"));
		resultV.setSize(pageV.getIntValue("size"));
		resultV.setTotalElements(pageV.getLongValue("totalElements"));
		resultV.setTotalPages(pageV.getIntValue("totalPages"));
		JSONArray content = pageV.getJSONArray("content");
		if(CommonUtils.isNotNullOrEmpty(content)) {
			resultV.setContent(content.toJavaList(clazz));
		}
		return resultV;
	}
	
	/**
	 * JSON
	 * @param response
	 * @return
	 */
	protected JSONObject convertToJSON(JSONObject result) {
		if(result.getIntValue("state") == 0) {
			return result.getJSONObject("data");
		} else {
			throw new GicException(result.getString("msg"));
		}
	}
}
