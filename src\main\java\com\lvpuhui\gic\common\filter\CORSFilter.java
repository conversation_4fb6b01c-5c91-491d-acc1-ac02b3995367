package com.lvpuhui.gic.common.filter;

import java.io.IOException;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;

import org.springframework.stereotype.Component;

/**
 * 跨域
 * 
 * <AUTHOR>
 *
 */
@Component
public class CORSFilter implements Filter {

	@Override
	public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
			throws IOException, ServletException {
		
		request.setCharacterEncoding("UTF-8");
		response.setContentType("text/html;charset=UTF-8");
		
		if (((HttpServletRequest) request).getMethod().equals("OPTIONS")) {
			response.getWriter().println("ok");
			return;
		}
		chain.doFilter(request, response);
	}

}
