package com.lvpuhui.gic.common.utils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.springframework.stereotype.Component;

/**
 * 密码校验
 * 
 * <AUTHOR>
 *
 */
@Component
public class PasswordValidator {

	/**
	 * 至少包含数字跟字母，可以有字符
	 */
	private final static String PASSWORD_PATTERN = "(?=.*([a-zA-Z].*))(?=.*[0-9].*)[a-zA-Z0-9-*/+.~!@#$%^&*()]{6,20}$";

	private Pattern pattern = null;

	public PasswordValidator() {
		pattern = Pattern.compile(PASSWORD_PATTERN);
	}

	public boolean validate(String password) {
		Matcher matcher = pattern.matcher(password);
		return matcher.matches();
	}
}
