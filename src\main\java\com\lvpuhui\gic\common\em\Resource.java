package com.lvpuhui.gic.common.em;

/**
 * 系统资源表(Restful API)
 * <AUTHOR>
 *
 */
public enum Resource {

	SCENARIO(1L, "场景", Service.ADMINISTRATIVE, "/scenario"),
	MODEL(2L, "模型", Service.ADMINISTRATIVE, "/model"),
	ANNOUNCEMENT(3L, "公告", Service.ADMINISTRATIVE, "/announcement"),
	CUSTOMER(4L, "客户", Service.ADMINISTRATIVE, "/customer"),
	STAFF(5L, "账户", Service.ACCOUNT, "/staff"),
	ORGANIZATION(6L, "组织", Service.ACCOUNT, "/organization"),
	ROLE(7L, "角色", Service.ACCOUNT, "/role"),
	ACCESS(8L, "权限", Service.ACCOUNT, "/access"),
	APPLICATION(9L, "应用", Service.ENTERPRISE, "/application"),
	ACT(10L, "行为", Service.ENTERPRISE, "/act"),
	ATTACH(11L, "附件", Service.FILE, "/attach"),
	TENANT(12L, "租户", Service.TENANT, "/tenant"),
	TENANT_ACCOUNT(13L, "租户账户", Service.TENANT, "/tenantAccount"),
	ENTERPRISE_INFORM(14L, "企业信息", Service.TENANT, "/enterpriseInform"),
	OPERATION_LOG(15L, "操作日志", Service.OPERATIONLOG, "/operationlog"),
	DASHBOARD_ENTERPRISE(15L, "数据统计-企业", Service.ANALYSIS, "/dashboard/enterprise"),
	DASHBOARD_SCENARIO(15L, "数据统计-场景", Service.ANALYSIS, "/dashboard/scenario"),
	DASHBOARD_APPLICATION(15L, "数据统计-应用", Service.ANALYSIS, "/dashboard/application"),
	DASHBOARD_ZONE(15L, "数据统计-地区", Service.ANALYSIS, "/dashboard/zone"),
	DASHBOARD_USER(15L, "数据统计-用户", Service.ANALYSIS, "/dashboard/user"),
	;
	private Long id;
	private String name;
	private Service service;
	private String mapping;

	private Resource(Long id, String name, Service service, String mapping) {
		this.id = id;
		this.name = name;
		this.service = service;
		this.mapping = mapping;
	}

	public Long getId() {
		return id;
	}
	
	public String getIdStr() {
		return String.valueOf(id);
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Service getService() {
		return service;
	}

	public void setService(Service service) {
		this.service = service;
	}

	public String getMapping() {
		return mapping;
	}

	public void setMapping(String mapping) {
		this.mapping = mapping;
	}
	
	public static Resource getById(Long id) {
		for(Resource r : Resource.values()) {
			if(r.getId().equals(id)) {
				return r;
			}
		}
		return null;
	}
}
