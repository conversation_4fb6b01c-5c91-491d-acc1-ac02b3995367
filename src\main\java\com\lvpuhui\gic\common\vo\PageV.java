package com.lvpuhui.gic.common.vo;

import org.springframework.data.domain.Page;

import java.util.List;

public class PageV<T> {

	private int size;// 每页大小
	private int page;// 页码
	private int totalPages;// 总页数
	private long totalElements;// 总数
	private List<T> content;// 数据

	public void setContent(List<T> content) {
		this.content = content;
	}

	public void initPage(Page<?> page) {
		this.totalElements = page.getTotalElements();
		this.totalPages = page.getTotalPages();
		this.size = page.getSize();
		this.page = page.getNumber() + 1;
	}

	public int getSize() {
		return size;
	}

	public void setSize(int size) {
		this.size = size;
	}

	public int getPage() {
		return page;
	}

	public void setPage(int page) {
		this.page = page;
	}

	public int getTotalPages() {
		return totalPages;
	}

	public void setTotalPages(int totalPages) {
		this.totalPages = totalPages;
	}

	public long getTotalElements() {
		return totalElements;
	}

	public void setTotalElements(long totalElements) {
		this.totalElements = totalElements;
	}

	public List<T> getContent() {
		return content;
	}
	
	public boolean isEmpty() {
		return content == null || content.size() == 0;
	}
}
