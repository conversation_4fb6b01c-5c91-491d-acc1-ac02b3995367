package com.lvpuhui.gic.common.operationlog;

import java.lang.reflect.Method;

import javax.servlet.http.HttpServletRequest;

import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import com.lvpuhui.gic.common.po.User;
import com.lvpuhui.gic.common.service.CommonRestTemplateService;
import com.lvpuhui.gic.common.utils.CommonUtils;

@Aspect
@Component
public class OperationLogAspect {

    @Autowired
    private CommonRestTemplateService commonRestTemplateService;

    @Pointcut("@annotation(com.lvpuhui.gic.common.operationlog.OperationLogAnnotation)")
    public void operationLogPoinCut() {
    }

    @AfterReturning(value = "operationLogPoinCut()", returning = "keys")
    public void saveOperationLog(JoinPoint joinPoint, Object keys) {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = (HttpServletRequest) requestAttributes.resolveReference(RequestAttributes.REFERENCE_REQUEST);
        OperationLogDto operationLog = new OperationLogDto();
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
		Method method = signature.getMethod();
		if(method.isAnnotationPresent(OperationLogAnnotation.class)) {
			User user = CommonUtils.getCurrentUser();
			if(user == null) {
				return;
			}
			OperationLogAnnotation opLog = method.getAnnotation(OperationLogAnnotation.class);
			OperationModule module = opLog.module();
			OperationType type = opLog.type();
			operationLog.setModule(module.name());
			operationLog.setType(type.name());
			operationLog.setRequestUri(request.getRequestURI());
			operationLog.setCreator(user.getId());//从jwt中提取
			operationLog.setUserName(user.getLoginName());
			operationLog.setUserType(user.getType());
			operationLog.setIp(CommonUtils.getIpAddr(request));
			commonRestTemplateService.saveOperationLog(operationLog);
		}
    }
}
