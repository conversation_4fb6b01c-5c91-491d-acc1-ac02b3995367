package com.lvpuhui.gic.common.dto;

import java.util.List;

/**
 * 行为到hive的存储对象
 * <AUTHOR>
 *
 */
public class ActToHive {

	private Long actId;
	private String actName;
	private Long tenantId;
	private Long applicationId;
	private String applicationName;
	private Long scenarioId;
	private Long modelId;
	private List<ActMappingToHive> mappings;
	private String optionValues;
	private String algorithm;//算法
	private String sqlText;//模型计算sql
	public Long getActId() {
		return actId;
	}
	public void setActId(Long actId) {
		this.actId = actId;
	}
	public Long getTenantId() {
		return tenantId;
	}
	public void setTenantId(Long tenantId) {
		this.tenantId = tenantId;
	}
	public Long getApplicationId() {
		return applicationId;
	}
	public void setApplicationId(Long applicationId) {
		this.applicationId = applicationId;
	}
	public Long getScenarioId() {
		return scenarioId;
	}
	public void setScenarioId(Long scenarioId) {
		this.scenarioId = scenarioId;
	}
	public Long getModelId() {
		return modelId;
	}
	public void setModelId(Long modelId) {
		this.modelId = modelId;
	}
	public List<ActMappingToHive> getMappings() {
		return mappings;
	}
	public void setMappings(List<ActMappingToHive> mappings) {
		this.mappings = mappings;
	}
	public String getOptionValues() {
		return optionValues;
	}
	public void setOptionValues(String optionValues) {
		this.optionValues = optionValues;
	}
	public String getActName() {
		return actName;
	}
	public void setActName(String actName) {
		this.actName = actName;
	}
	public String getApplicationName() {
		return applicationName;
	}
	public void setApplicationName(String applicationName) {
		this.applicationName = applicationName;
	}
	public String getAlgorithm() {
		return algorithm;
	}
	public void setAlgorithm(String algorithm) {
		this.algorithm = algorithm;
	}
	public String getSqlText() {
		return sqlText;
	}
	public void setSqlText(String sqlText) {
		this.sqlText = sqlText;
	}
}
